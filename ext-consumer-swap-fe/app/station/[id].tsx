import { DirectionsRoute, fetchDirections } from "@/apis/mapbox";
import { Button, ButtonVariant } from "@/components/Button";
import { CloseButton } from "@/components/CloseButton";
import { DirectionsMap } from "@/components/DirectionsMap";
import { NavigateButton } from "@/components/NavigateButton";
import { SnappableBottomSheet } from "@/components/SnappableBottomSheet";
import { FontStyles } from "@/constants/FontStyles";
import { useEnhancedSwapStation } from "@/contexts/useEnhancedSwapStationContext";
import { useUserLocationContext } from "@/contexts/useUserLocationContext";
import { EnhancedSwapStation } from "@/hooks/useSwapStationMiddleware";
import { getColor } from "@/utils/style";
import { router, useLocalSearchParams } from "expo-router";
import { Bookmark, Calendar, CornerUpRight } from "lucide-react-native";

import { useState } from "react";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { FlatList, Text, View, useWindowDimensions } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";

const StationInfo = ({ station, directions }: { station: EnhancedSwapStation; directions: DirectionsRoute | null }) => {
  return (
    <View style={{ flexDirection: "column", gap: 4 }}>
      <Text style={[FontStyles.heading1, { color: getColor("foreground") }]}>{station.thingName}</Text>
      {directions && (
        <View style={{ flexDirection: "row", gap: 4 }}>
          <Text style={[FontStyles.body, { color: getColor("foreground", "half") }]}>
            {(directions.distance / 1000).toFixed(1)} km
          </Text>
          <Text style={[FontStyles.body, { color: getColor("foreground", "half") }]}>•</Text>
          <Text style={[FontStyles.body, { color: getColor("foreground", "half") }]}>
            {Math.round(directions.duration / 60)} min
          </Text>
        </View>
      )}
    </View>
  );
};

const StationMeta = ({ station }: { station: EnhancedSwapStation }) => {
  const { t } = useTranslation();
  return (
    <View
      style={{
        flexDirection: "row",
        justifyContent: "space-between",
        gap: 16,
      }}
    >
      <View style={{ flexDirection: "column", gap: 0, flexShrink: 1 }}>
        <Text style={[FontStyles.caption, { color: getColor("foreground", "half") }]}>{t("uiPrompts.location")}</Text>
        <Text style={[FontStyles.body, { color: getColor("foreground") }]}>{station.address}</Text>
      </View>

      <View>
        <NavigateButton latitude={station.latitude} longitude={station.longitude} />
      </View>
    </View>
  );
};

const StationWarning = () => {
  const { t } = useTranslation();
  return (
    <View
      style={{
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        paddingVertical: 12,
        gap: 0,
        flexBasis: "50%",
        borderRadius: 16,
        backgroundColor: getColor("destructive", "dim"),
      }}
    >
      <Text style={[FontStyles.bodyEmphasized, { color: getColor("destructive") }]}>
        {t("uiPrompts.noBatteriesAvailable")}
      </Text>
      <Text style={[FontStyles.footnote, { color: getColor("destructive") }]}>{t("uiPrompts.returnOnlyStation")}</Text>
    </View>
  );
};

const StationActions = ({ station }: { station: EnhancedSwapStation }) => {
  const { refresh } = useEnhancedSwapStation();
  const { t } = useTranslation();

  const isBatteryDataLoading = station.batteryManufacturerIds === undefined;
  const batteryCount = station.batteryManufacturerIds?.length ?? 0;

  const returnBattery = (
    <Button
      text={t("uiPrompts.returnBattery")}
      onPress={() => {
        refresh();
        router.push(`/reservation/return?stationId=${station.thingId}`);
      }}
      size={ButtonVariant.Size.MEDIUM}
      color={ButtonVariant.Color.SECONDARY}
    />
  );

  // Show loading state while battery data is being fetched
  if (isBatteryDataLoading) {
    return (
      <View style={{ gap: 16 }}>
        <Button
          text={t("uiPrompts.swapBattery")}
          subText={t("uiPrompts.loadingBatteries")}
          onPress={() => {}} // Disabled while loading
          size={ButtonVariant.Size.LARGE}
          color={ButtonVariant.Color.SECONDARY} // Use secondary color to indicate disabled
          disabled={true}
        />
        <Button
          text={t("uiPrompts.rentBattery")}
          onPress={() => {}} // Disabled while loading
          size={ButtonVariant.Size.MEDIUM}
          color={ButtonVariant.Color.SECONDARY}
          disabled={true}
        />
        {returnBattery}
      </View>
    );
  }

  if (batteryCount === 0) {
    return (
      <View style={{ gap: 16 }}>
        <StationWarning />
        {returnBattery}
      </View>
    );
  }

  return (
    <View style={{ gap: 16 }}>
      <Button
        text={t("uiPrompts.swapBattery")}
        subText={`${batteryCount} ${t("uiPrompts.batteriesAvailable")}`}
        onPress={() => {
          refresh();
          router.push(`/reservation/new?stationId=${station.thingId}&type=swap`);
        }}
        size={ButtonVariant.Size.LARGE}
        color={ButtonVariant.Color.PRIMARY}
      />
      <Button
        text={t("uiPrompts.rentBattery")}
        onPress={() => {
          refresh();
          router.push(`/reservation/new?stationId=${station.thingId}&type=rent`);
        }}
        size={ButtonVariant.Size.MEDIUM}
        color={ButtonVariant.Color.SECONDARY}
      />
      {returnBattery}
      {/* <NavigateButton
        latitude={station.latitude}
        longitude={station.longitude}
      /> */}
    </View>
  );
};

const Footer = ({ station }: { station: EnhancedSwapStation }) => {
  const { t } = useTranslation();
  return (
    <View style={{ flexDirection: "column", gap: 2 }}>
      <Text style={[FontStyles.footnote, { color: getColor("destructive", "half") }]}>
        {t("uiPrompts.locationId")}: {station.thingId}
      </Text>
      {/* <Text style={[FontStyles.footnote, { color: getColor('foreground', 'half') }]}>
        Last Updated: {lastUpdated.toLocaleString()}
      </Text> */}
    </View>
  );
};

export default function StationDetails() {
  // TODO: get station info dynamically
  const { id } = useLocalSearchParams();

  const { stations } = useEnhancedSwapStation();
  const station = stations.find((station) => station.thingId === id);

  const { location } = useUserLocationContext();
  const [directions, setDirections] = useState<DirectionsRoute | null>(null);

  useEffect(() => {
    if (!location || !station) return;
    const fetchDirectionsAsync = async () => {
      const directions = await fetchDirections(location, stationLocation);
      setDirections(directions);
    };
    fetchDirectionsAsync();
  }, [station, location]);

  const insets = useSafeAreaInsets();
  const { height } = useWindowDimensions();

  const initialBottomSheetHeight = (height - insets.top - insets.bottom) / 2;

  if (!station) {
    return null;
  }

  const stationLocation = {
    latitude: station?.latitude,
    longitude: station?.longitude,
  };

  return (
    <View style={{ flex: 1 }}>
      <DirectionsMap
        from={location}
        to={stationLocation}
        directions={directions}
        insets={{
          top: insets.top,
          bottom: initialBottomSheetHeight + 16,
          left: insets.left + 16,
          right: insets.right + 16,
        }}
      />
      <SnappableBottomSheet initialHeight={initialBottomSheetHeight}>
        <View style={{ flex: 1, padding: 16, gap: 32 }}>
          <CloseButton position="absolute" />
          <StationInfo station={station} directions={directions} />
          <StationMeta station={station} />
          <StationActions station={station} />
          <Footer station={station} />
        </View>
      </SnappableBottomSheet>
    </View>
  );
}
