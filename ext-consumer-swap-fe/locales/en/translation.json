{"uiPrompts": {"findSwap": "Find a swap station", "findByCharge": "Find Stations by Charge", "selectBatteryLevel": "Select battery level and passengers to see stations within range", "batteryLevel": "Battery Level", "riders": "Riders", "batteriesAvailable": "batteries available", "loadingBatteries": "Loading batteries...", "outOfRange": "out of range", "location": "Location", "navigate": "Navigate", "swapBattery": "Swap Battery", "rentBattery": "Rent Battery", "returnBattery": "Return Battery", "noBatteriesAvailable": "No Batteries Available", "returnOnlyStation": "Returns only at this station", "locationId": "Location ID", "reservationLocation": "Reservation Location", "directions": "Directions", "imHere": "I'm Here", "pleaseClickArrive": "Please click the button below when you arrive at the station.", "arrivedAtStation": "Arrived At Station", "arrivedAtStationDesc": "Click the button below to begin your {{action}}. We will guide you through all the steps.", "start": "Start {{action}}", "returnUsedBattery": "Return Used Battery To", "door": "DOOR", "returnUsedBatteryDesc": "Please return your used battery to door", "gettingBatteryDetails": "Getting battery details...", "gettingDoor": "Getting door...", "communicateStation1": "Communicating with station (1)...", "communicateStation2": "Communicating with station (2)...", "connectingToStation": "Connecting to station...", "continueToInstall": "Continue to Install", "complete": "{{action}} Complete", "newReservationAction": "New {{action}} Reservation", "reservationEndTime": "Reservation End Time", "confirmReservation": "Confirm Reservation", "expectedBatteryToSwap": "Expected Battery to Swap", "overrideBatteryToSwap": "Override Battery to Swap", "myReservationAction": "My {{action}} Reservation", "extend": "Extend", "reservedBatteryDoor": "Reserved Battery Door", "batteryId": "Battery ID", "reservationStatus": "Reservation Status", "reservationId": "Reservation ID", "cancelReservation": "Cancel Reservation", "removeNewBatteryFrom": "Remove New Battery From", "removeNewBatteryPlease": "Please remove the new battery from door", "completeInstall": "Complete Install", "completeThanks": "Thanks for completing your {{action}} with <PERSON><PERSON><PERSON>. Your receipt will be emailed soon.", "bookAgain": "Book again", "getHelp": "Get Help", "tryAgain": "Try Again", "cancel": "Cancel", "transactionHistory": "Transaction History", "accountSettings": "Account <PERSON><PERSON>", "help": "Help", "logout": "Log out", "contactUs": "Contact Us", "termsOfService": "Terms of Service", "privacyPolicy": "Privacy Policy", "loginAndPassword": "Login and Password", "billing": "Billing", "notificationSettings": "Notification Settings", "languages": "Languages", "availableBalance": "Available Balance", "addMoney": "Add Money", "receipt": "Receipt", "swapFee": "<PERSON><PERSON><PERSON>", "tax": "Tax", "total": "Total", "paidBy": "Paid by {{type}}", "reservationTimeline": "Reservation Timeline", "swapBooked": "<PERSON><PERSON><PERSON> Booked", "reservationExtended": "Reservation Extended", "swapCompleted": "Swap Completed"}}