{"uiPrompts": {"findSwap": "Temukan stasiun penukaran", "findByCharge": "<PERSON><PERSON><PERSON>", "selectBatteryLevel": "<PERSON><PERSON><PERSON> tingkat baterai dan jumlah penumpang untuk melihat stasiun dalam jangkauan", "batteryLevel": "Tingkat baterai", "riders": "Penumpang", "batteriesAvailable": "baterai tersedia", "loadingBatteries": "Memuat baterai...", "outOfRange": "di luar jang<PERSON>uan", "location": "<PERSON><PERSON>", "navigate": "Na<PERSON><PERSON><PERSON>", "swapBattery": "<PERSON><PERSON>", "rentBattery": "Sewa Baterai", "returnBattery": "Ke<PERSON>likan <PERSON>i", "noBatteriesAvailable": "Tidak ada baterai tersedia", "returnOnlyStation": "<PERSON>ya kembalikan di stasiun ini", "locationId": "ID Lokasi", "reservationLocation": "Lokasi Reservasi", "directions": "Panduan Arah", "imHere": "<PERSON><PERSON>", "pleaseClickArrive": "<PERSON><PERSON>an tekan tombol di bawah jika Anda sudah sampai di stasiun.", "arrivedAtStation": "Sudah Tiba di Stasiun", "arrivedAtStationDesc": "<PERSON><PERSON> tombol di bawah ini untuk memulai {{action}}. <PERSON><PERSON> akan membimbing <PERSON>a selama prosesnya.", "start": "<PERSON><PERSON> {{action}}", "returnUsedBattery": "Kembalikan Baterai Ke", "door": "PINTU", "returnUsedBatteryDesc": "<PERSON><PERSON> kembalikan baterai Anda ke pintu", "gettingBatteryDetails": "Mendapatkan detail baterai...", "gettingDoor": "Mendapatkan pintu...", "communicateStation1": "Berkomunikasi dengan stasiun (1)...", "communicateStation2": "Berkomunikasi dengan s<PERSON>n (2)...", "connectingToStation": "Menghubungkan ke stasiun...", "continueToInstall": "Lanjutkan untuk Memasang", "complete": "{{action}} <PERSON><PERSON><PERSON>", "newReservationAction": "Reser<PERSON>i {{action}} <PERSON>u", "reservationEndTime": "<PERSON><PERSON><PERSON>", "confirmReservation": "Konfirma<PERSON>", "expectedBatteryToSwap": "<PERSON><PERSON><PERSON> yang <PERSON>", "overrideBatteryToSwap": "Ganti Baterai secara Manual", "myReservationAction": "<PERSON><PERSON><PERSON><PERSON> {{action}} <PERSON>a", "extend": "Perpanjang", "reservedBatteryDoor": "<PERSON><PERSON><PERSON> yang Direservasi", "batteryId": "ID Baterai", "reservationStatus": "Status Reservasi", "reservationId": "ID Reservasi", "cancelReservation": "Batalkan Reservasi", "removeNewBatteryFrom": "Lepaskan Baterai Baru Dari", "removeNewBatteryPlease": "<PERSON><PERSON><PERSON> lepaskan baterai baru dari pintu", "completeInstall": "<PERSON><PERSON><PERSON><PERSON>", "completeThanks": "<PERSON><PERSON> kasih telah <PERSON> {{action}} <PERSON><PERSON>. Struk Anda akan segera dikirim melalui email.", "bookAgain": "Pesan lagi", "getHelp": "Dapatkan Bantuan", "tryAgain": "<PERSON><PERSON>", "cancel": "Batalkan", "transactionHistory": "Riwayat Transaksi", "accountSettings": "<PERSON><PERSON><PERSON><PERSON>", "help": "Bantuan", "logout": "<PERSON><PERSON><PERSON>", "contactUs": "<PERSON><PERSON><PERSON><PERSON>", "termsOfService": "<PERSON><PERSON><PERSON>", "privacyPolicy": "<PERSON><PERSON><PERSON><PERSON>", "loginAndPassword": "<PERSON><PERSON><PERSON> dan <PERSON>", "billing": "<PERSON><PERSON><PERSON>", "notificationSettings": "Pengatura<PERSON>", "languages": "Bahasa", "availableBalance": "<PERSON><PERSON>", "addMoney": "<PERSON><PERSON>", "receipt": "<PERSON><PERSON><PERSON>", "swapFee": "<PERSON><PERSON><PERSON>", "tax": "<PERSON><PERSON>", "total": "Total", "paidBy": "<PERSON><PERSON><PERSON> dengan {{type}}", "reservationTimeline": "Linimasa Reservasi", "swapBooked": "<PERSON><PERSON><PERSON>", "reservationExtended": "Reservasi <PERSON>", "swapCompleted": "<PERSON><PERSON><PERSON>"}}